import Image from "next/image";
import LanguageSelector from "./translate";
import { useAtom } from "jotai";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export const FormLayout = ({ pageQuery, children }: any) => {
  const [fontSize] = useAtom(fontSizeAtom);
  return (
    <main className="bg-on-background overflow-y-auto fixed inset-0">
      <div
        className="w-full flex flex-col items-center min-h-screen px-2 md:px-0 bg-cover bg-center md:bg-contain md:bg-no-repeat md:bg-right"
        style={{
          backgroundImage: `url(${pageQuery?.layout?.backgroundImage})`,
        }}
      >
        {/* Header section */}
        <header
          className={`w-full flex flex-col items-center ${
            pageQuery?.languageData
              ? "justify-center md:pt-12"
              : "justify-start md:pt-8"
          } `}
        >
          <div
            className={`flex items-center  w-full ${
              pageQuery?.languageData
                ? "max-md:px-0 md:px-12 xl:px-16 justify-between"
                : "justify-center"
            }`}
          >
            <div
              className={`mt-8 md:mt-0`}
              style={{
                width: pageQuery?.logoInfo?.width,
                height: pageQuery?.logoInfo?.height,
              }}
            >
              <Image
                src={pageQuery?.logoInfo?.signedUrl}
                alt="brand_logo"
                priority
                className={`mb-1 ${
                  pageQuery?.languageData
                    ? "flex justify-end md:ml-[32px] lg:ml-[10%] xl:ml-[80%] 2xl:ml-[90%]"
                    : "flex justify-center"
                }`}
                width={pageQuery?.logoInfo?.width}
                height={pageQuery?.logoInfo?.height}
              />
            </div>
            {pageQuery?.languageData &&
              pageQuery?.languageData.canShowLanguageSwitch && (
                <div className="ml-auto" role="button" tabIndex={0}>
                  <LanguageSelector languageData={pageQuery?.languageData} />
                </div>
              )}
          </div>
        </header>

        {/* Content area for children */}
        <section
          className={`w-full flex flex-col items-center ${
            pageQuery?.languageData ? "py-8 md:py-24 px-0 md:px-8" : "py-8"
          }`}
        >
          {children}
        </section>

        <div className="flex-grow" />
        {/* Footer section */}
        <footer className="mb-4 mt-4 md:mb-8 md:mt-0 px-4 md:px-8">
          <p
            className="text-text-secondary text-xs text-center"
            style={getBrandSpecificFontStyle(fontSize, "timeline-label")}
          >
            {pageQuery?.layout?.collegeInfo?.text}{" "}
            <a
              href={pageQuery?.layout?.collegeInfo?.linkUrl}
              target="_blank"
              className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
              aria-label={`${pageQuery?.layout?.collegeInfo?.linkText} (opens in new tab)`}
              rel="noreferrer"
            >
              {pageQuery?.layout?.collegeInfo?.linkText}
            </a>
          </p>
          <span
            className="w-full text-text-secondary text-xs flex justify-center my-[2px] pb-[2px]"
            style={getBrandSpecificFontStyle(fontSize, "timeline-label")}
          >
            v{process.env.VERSION}
          </span>
        </footer>
      </div>
    </main>
  );
};
